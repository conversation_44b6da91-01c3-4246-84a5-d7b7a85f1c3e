import { notification } from 'antd';
import { useEffect, useLayoutEffect, useMemo, useState } from 'react';
import type { FC } from 'react';
import { AppHeader } from 'widgets/AppHeader';
import { FiltersDrawer, filtersDrawerStore } from 'widgets/FiltersDrawer';
import { NestedLazyTable } from 'widgets/NestedLazyTable';
import { NewWorkGroupControlTabs } from 'widgets/NewWorkGroupControlTabs';
import { NewWorkGroupTabs } from 'widgets/NewWorkGroupTabs';
import {
  SmartGridHandler,
  smartGridHandlerModule,
} from 'widgets/SmartGridHandler';
import {
  SmartGridTab,
  smartGridTabStore,
  smartGridTabConfig,
  DrillFiltersToNestedTable,
  GetTableDataParams,
} from 'widgets/SmartGridTab';
import { WorkGroupCatalogs } from 'widgets/WorkGroupCatalogs';
import { WorkGroupCreation } from 'widgets/WorkGroupCreation';
import { WorkGroupCreationPlan } from 'widgets/WorkGroupCreationPlan';
import { WorkGroupSettings } from 'widgets/WorkGroupSettings';
import { dataGridStore, TableColumnData } from 'features/DataGrid';
import { createDownloadModal } from 'features/DownloadModal';
import { FiltersConfigFileControls } from 'features/FiltersConfigFileControls';
import { createTableConfigModal } from 'features/TableConfigModal';
import {
  closableTabsWithMainStore,
  ClosableTabsWithMain,
  closableTabsWithMainConfig,
} from 'entities/ClosableTabsWithMain';
import { EditedRowLabel } from 'entities/EditedRowLabel';
import { permissionsConfig } from 'entities/Permissions';
import { apiUrls } from 'shared/api';
import {
  createConfirmModal,
  useAppSelector,
  useCreateSliceActions,
  usePopupsToggle,
} from 'shared/model';
import { ApiContainer } from 'shared/ui';
import { PageContainer } from 'shared/ui/PageContainer';
import { WorkGroupsProps } from '..';
import { isLazyReport, popupsInitial, reportEndpointsMap } from '../config';
import { checkCanOpen } from '../lib';
import { hooks } from '../store';
import { WGSpace } from './WGSpace';
import { WGSwitcher } from './WGSwitcher';

const renderNestedTable: DrillFiltersToNestedTable =
  (parentColumns, filters, validateAccess) =>
  (endpoint, groupValue, parentRow, onRowClick) =>
    (
      <NestedLazyTable
        endpoint={endpoint}
        groupValue={groupValue}
        parentRow={parentRow}
        renderCustomRow={(row) => <EditedRowLabel row={row} />}
        onRowClick={onRowClick}
        filters={filters || []}
        parentColumns={parentColumns}
        validateAccess={validateAccess}
      />
    );

export const WorkGroups: FC<WorkGroupsProps> = ({
  title,
  tabTitle,
  tableEndpoint,
  withWGSwitcher,
  buttonsRenderer,
}) => {
  const { FIRST_PAGE, EMPTY_GROUPING_VALUE } = smartGridTabConfig.constants;

  const [isFirstLoad, setFirstLoad] = useState(true);
  const [isNotice, redirect] = hooks.useNotices(withWGSwitcher);
  const [activeKey, onTabClick, addTab, additionalTabs, closeTab] =
    closableTabsWithMainStore.hooks.useTabs(tableEndpoint, {
      isSingleTab: false,
      isAdditionDisabled: Boolean(isNotice),
    });

  /** Хук для получения данных из квери парамсов */
  const [
    currentPage,
    pageSize,
    sortColumnId,
    sortOrder,
    setPage,
    setSort,
    resetQuery,
  ] = smartGridTabStore.hooks.useStateWithQuery();

  const [{ filter, ...popups }, toggle] = usePopupsToggle(popupsInitial);

  const {
    selectedFiltersCount,
    selectedFilters,
    isPending: isFiltersPending,
    lastCachedUrl,
    error: filtersError,
  } = useAppSelector(filtersDrawerStore.selectors.selectedFiltersApiSelector);

  const [isSmartGrouped, groupValue, setSmartGrouped] =
    smartGridHandlerModule.useSmartGrouped(tableEndpoint);

  const { clearFilters } = useCreateSliceActions(
    filtersDrawerStore.reducers.slice.actions,
  );

  const [tableData, tableState, getTableData, resetTableData] =
    smartGridTabStore.hooks.useTableData(tableEndpoint);

  const {
    currentSelectedProfile,
    updatedColumns, // Колонки
    tableSizes,
    onResize,
    onProfileSet,
  } = dataGridStore.hooks.useTableProfile(tableData.columns, tableEndpoint);

  const reportColumns: Partial<TableColumnData>[] = useMemo(
    () =>
      updatedColumns.map(({ key, title: columnTitle }) => ({
        key,
        width: tableSizes[String(columnTitle)] || tableSizes[key!] || 150,
      })),
    [tableSizes, updatedColumns],
  );

  const [getBadgeCount, badgeCount] = hooks.useBadgeData(
    buttonsRenderer === 'WGCButtons',
  );

  const [value, suffix] = hooks.useSpaceInfo(buttonsRenderer === 'WGButtons');
  const buttons = hooks.useButtonsRenderer(buttonsRenderer, toggle, {
    badgeCount,
  });

  useEffect(() => {
    const { sortValue } = currentSelectedProfile || {};
    const { value: sort, columnUuid } = sortValue || {};

    if (sortColumnId === '' && sort && columnUuid) {
      setSort(sort, columnUuid);
    }
  }, [currentSelectedProfile, sortColumnId, setSort]);

  /** Получение данных при загрузке страницы */
  useLayoutEffect(() => {
    const isInitialLoad =
      lastCachedUrl === apiUrls.defaultPages.filters(tableEndpoint) &&
      isFirstLoad;

    if (isInitialLoad) {
      getTableData(selectedFilters)({
        page: currentPage,
        size: pageSize,
        sort: sortColumnId,
        direction: sortOrder,
      });
      setFirstLoad(false);
    }
  }, [tableEndpoint, selectedFilters, lastCachedUrl]); // eslint-disable-line

  /** Ресет при смене ендпоинта */
  useEffect(() => {
    resetTableData();
    setFirstLoad(true);
  }, [tableEndpoint]); // eslint-disable-line

  return (
    <PageContainer containerKey={tableEndpoint}>
      <AppHeader title={title}>
        <WGSpace
          suffix={suffix}
          value={value}
          isWG={buttonsRenderer === 'WGButtons'}
        />
        <WGSwitcher
          isNotices={isNotice}
          onChange={() => {
            redirect();
            setSmartGrouped(false, null);
          }}
          isPending={tableState.isPending}
        />

        <FiltersConfigFileControls
          hidden={activeKey !== closableTabsWithMainConfig.FIRST_TAB}
          additionalButtons={buttons}
          isFiltersDisabled={
            tableState.isPending || isSmartGrouped || isFiltersPending
          }
          handlers={{
            openConfig: () => {
              createTableConfigModal({
                endpoint: tableEndpoint,
                tableSizes,
                columns: tableData.columns,
                onProfileSet: async (profile) => {
                  onProfileSet(profile);

                  if (profile?.sortValue) {
                    await setSort(
                      profile?.sortValue?.value || undefined,
                      profile?.sortValue?.columnUuid,
                    );
                  }

                  getTableData(selectedFilters)({
                    page: currentPage,
                    size: pageSize,
                    sort: sortColumnId,
                    direction: sortOrder,
                  });
                },
                currentSelectedProfile,
              });
            },
            openDownload: () =>
              createDownloadModal({
                isLazyReport: isLazyReport(tableEndpoint),
                url: tableEndpoint,
                reportEndpoint: reportEndpointsMap[tableEndpoint] || undefined,
                isExportToExcel: true,
                reportControlDTO: {
                  inputData: selectedFilters,
                  reportSettings: {},
                  columns: reportColumns,
                  idAndOrders:
                    Boolean(sortColumnId) && Boolean(sortOrder)
                      ? [
                          {
                            columnId: sortColumnId,
                            descending: sortOrder === 'DESC',
                          },
                        ]
                      : [],
                },
              }),
            clearFilters: createConfirmModal({
              isCallback: true,
              title: 'Очистка',
              message: 'Вы действительно хотите очистить фильтры?',
              onConfirm: () => {
                clearFilters({ clearApplyFilters: true });
                getTableData([])({ page: FIRST_PAGE, size: pageSize });
              },
            }) as Callback,

            openFilters: () => toggle('filter'),
            refetchTable: () =>
              getTableData(selectedFilters)({
                page: currentPage,
                size: pageSize,
                sort: sortColumnId,
                direction: sortOrder,
              }),
          }}
          selectedFiltersCount={selectedFiltersCount}
        />
      </AppHeader>

      <ApiContainer
        error={tableState.error || filtersError}
        isPending={
          (tableState.isPending && !tableState.isResEnd) || isFiltersPending
        }
      >
        <ClosableTabsWithMain
          activeKey={activeKey}
          additionalTabs={additionalTabs}
          onClose={closeTab}
          onTabClick={onTabClick}
          MainTab={
            <SmartGridTab
              isCursorPointer={!isNotice}
              validateAccess={({ rowId }) =>
                isSmartGrouped ? true : checkCanOpen(rowId)
              }
              tableSizes={tableSizes}
              onResize={onResize}
              endpoint={tableEndpoint}
              group={{
                isGrouped: isSmartGrouped,
                groupValue: groupValue || EMPTY_GROUPING_VALUE,
              }}
              pageSize={pageSize}
              sort={{ sortColumnId, sortOrder }}
              onSort={async (newSortOrder, newSortColumnId) => {
                await setSort(newSortOrder, newSortColumnId);
                await getTableData(selectedFilters)({
                  page: currentPage,
                  size: pageSize,
                  sort: newSortColumnId,
                  direction: newSortOrder,
                });
              }}
              onTabAddition={(rowId, tabName, isDoubleClick) => {
                const workGroupTabs = {
                  '/krg3': <NewWorkGroupTabs additionalParams={rowId || {}} />,
                  '/krg4': (
                    <NewWorkGroupControlTabs additionalParams={rowId || {}} />
                  ),
                };

                const hasPermissions =
                  checkCanOpen(rowId) &&
                  ['/krg3', '/krg4'].includes(tableEndpoint);

                if (hasPermissions) {
                  addTab(workGroupTabs[tableEndpoint as '/krg3' | '/krg4'])(
                    rowId,
                    tabName,
                    isDoubleClick,
                  );
                } else if (!isNotice) {
                  notification.warn({
                    message:
                      permissionsConfig.warnMessages.noPermissionsDefault(
                        'просмотр кабинета',
                      ),
                  });
                }
              }}
              columns={isSmartGrouped ? tableData.columns : updatedColumns}
              rows={tableData.rows}
              isPending={tableState.isPending}
              onPagination={async (newPage, newPageSize) => {
                await setPage(newPage, newPageSize);

                if (!isSmartGrouped) {
                  await getTableData(selectedFilters)({
                    page: newPage,
                    size: newPageSize,
                    sort: sortColumnId,
                    direction: sortOrder,
                  });
                }
              }}
              total={tableData.pagination.total}
              footerAdditionalComponent={
                <SmartGridHandler
                  isPending={tableState.isPending}
                  endpoint={tableEndpoint}
                  withSelect
                  onChange={async (isGrouped, selectValue) => {
                    const tableDataOptions: GetTableDataParams = {
                      page: FIRST_PAGE,
                      size: pageSize,
                    };

                    if (isGrouped) {
                      tableDataOptions.group =
                        selectValue || EMPTY_GROUPING_VALUE;
                    }

                    await getTableData(selectedFilters)(tableDataOptions);
                    await resetQuery();
                    setSmartGrouped(isGrouped, selectValue);
                  }}
                />
              }
              page={currentPage}
              renderNestedTable={renderNestedTable(
                tableData.columns,
                selectedFilters,
                ({ rowId }) => checkCanOpen(rowId),
              )}
            />
          }
          title={tableData?.reportTitle || tabTitle}
        />
      </ApiContainer>

      {/* Попапы */}
      <FiltersDrawer
        isOpened={filter}
        handleClose={() => toggle('filter', false)}
        filtersEndpoint={apiUrls.defaultPages.filters(tableEndpoint)}
        onSubmit={async (filters) => {
          await setPage(FIRST_PAGE);
          await getTableData(filters)({
            page: FIRST_PAGE,
            size: pageSize,
            sort: sortColumnId,
            direction: sortOrder,
          });
        }}
      />
      <WorkGroupCatalogs
        title="Шаблоны структуры каталогов ЭПП"
        onClose={() => toggle('tree')}
        isOpened={popups.tree}
      />

      <WorkGroupSettings
        isOpened={popups.settings}
        title="Общие настройки КРГ"
        onClose={() => toggle('settings')}
      />
      {popups.creation && (
        <WorkGroupCreation
          onClose={() => toggle('creation')}
          title="Создание Кабинета Рабочей Группы"
          refetch={async () => {
            setSmartGrouped(false, null);
            await resetQuery();
            await getTableData()({ page: FIRST_PAGE, size: pageSize });
          }}
        />
      )}
      {popups.createPlan && (
        <WorkGroupCreationPlan
          getBadgeCount={getBadgeCount}
          title="План создания КРГ"
          handleClose={() => toggle('createPlan')}
          refetch={async () => {
            await resetQuery();
            setSmartGrouped(false, null);
            await getTableData()({ page: FIRST_PAGE, size: pageSize });
          }}
        />
      )}
    </PageContainer>
  );
};
